{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__860be4f0._.js", "server/edge/chunks/edge-wrapper_1985d09c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Bo+sQ9ioaiyiLUwLjzd1udTV8v8fmaLeb82li3vq6m4=", "__NEXT_PREVIEW_MODE_ID": "3f3147432a3e621c5aa126147d9c3545", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "2251767b10b79af6566ebf53f4644f049d79abd3ecbe748c419a4cd9b6c207ee", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bf6074fc2e81bef301185ad3d0bf9372bb0ec81dbd4a2a95c940d704d9091d1e"}}}, "sortedMiddleware": ["/"], "functions": {}}